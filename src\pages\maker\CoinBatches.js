import React, { useState, useEffect, useCallback } from 'react';
import { Container, Row, Col, Card, Table, Badge, Form, InputGroup, Pagination, Button } from 'react-bootstrap';
import { FaDownload, FaSync } from 'react-icons/fa';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import { useDataCache } from '../../hooks/useDataCache';

const CoinBatches = () => {
    const { t } = useTranslation();
    const [searchTerm, setSearchTerm] = useState('');
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [filteredBatches, setFilteredBatches] = useState([]);

    // Pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [batchesPerPage] = useState(10);
    const [paginatedBatches, setPaginatedBatches] = useState([]);

    // 数据获取函数
    const fetchBatches = useCallback(async () => {
        const supabase = getSupabase();
        if (!supabase) return null;

        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
            return null; // User not logged in
        }

        // Fetch distribution batches with related information
        const { data, error } = await supabase
            .from('distribution_batches')
            .select(`
                id,
                maker_id,
                agent_id,
                currency_code,
                product_id,
                shares,
                batch_amount,
                per_share_amount,
                status,
                created_at,
                distributed_at,
                maker:maker_profiles!maker_id (
                    user_id,
                    users (
                        email
                    )
                ),
                agent:agent_profiles!agent_id (
                    user_id,
                    users (
                        email
                    )
                ),
                product:products!product_id (
                    name,
                    category
                ),
                currency:currencies!currency_code (
                    code
                )
            `)
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Error fetching distribution batches:', error);
            throw error;
        }

        return data;
    }, []);

    // 使用数据缓存Hook
    const {
        data: batches,
        loading,
        refreshing,
        refresh: handleRefresh,
        getLastUpdateText
    } = useDataCache(fetchBatches, 'BATCHES', {
        refreshOnVisibilityChange: true
    });

    // Filter batches based on search criteria
    useEffect(() => {
        let filtered = batches || [];

        // Search by agent email or product name
        if (searchTerm) {
            filtered = filtered.filter(batch =>
                batch.agent?.users?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                batch.product?.name?.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Filter by date range
        if (startDate) {
            filtered = filtered.filter(batch =>
                new Date(batch.created_at) >= new Date(startDate)
            );
        }
        if (endDate) {
            filtered = filtered.filter(batch =>
                new Date(batch.created_at) <= new Date(endDate)
            );
        }

        setFilteredBatches(filtered);
        setCurrentPage(1); // Reset to first page when filters change
    }, [batches, searchTerm, startDate, endDate]);

    // Paginate filtered batches
    useEffect(() => {
        const indexOfLastBatch = currentPage * batchesPerPage;
        const indexOfFirstBatch = indexOfLastBatch - batchesPerPage;
        const currentBatches = filteredBatches.slice(indexOfFirstBatch, indexOfLastBatch);
        setPaginatedBatches(currentBatches);
    }, [filteredBatches, currentPage, batchesPerPage]);

    // Pagination handlers
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const totalPages = Math.ceil(filteredBatches.length / batchesPerPage);

    // Export filtered batches to CSV
    const exportToCSV = () => {
        if (filteredBatches.length === 0) {
            alert(t('no_data_to_export'));
            return;
        }

        // Define CSV headers
        const headers = [
            t('batch_id'),
            t('agent'),
            t('product_name'),
            t('currency'),
            t('shares'),
            t('batch_amount'),
            t('per_share_amount'),
            t('status'),
            t('created_at'),
            t('distributed_at')
        ];

        // Convert data to CSV format
        const csvData = filteredBatches.map(batch => [
            batch.id,
            batch.agent?.users?.email || '-',
            batch.product?.name || '-',
            batch.currency_code || '-',
            batch.shares ? Number(batch.shares).toFixed(2) : '0',
            batch.batch_amount ? Number(batch.batch_amount).toFixed(6) : '0',
            batch.per_share_amount ? Number(batch.per_share_amount).toFixed(6) : '0',
            t(batch.status) || 'unknown',
            new Date(batch.created_at).toLocaleString(),
            batch.distributed_at ? new Date(batch.distributed_at).toLocaleString() : '-'
        ]);

        // Combine headers and data
        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Add UTF-8 BOM to ensure proper encoding for Japanese characters
        const BOM = '\uFEFF';
        const csvWithBOM = BOM + csvContent;

        // Create and download CSV file with proper UTF-8 encoding
        const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `coin_batches_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'pending':
                return 'warning';
            case 'distributed':
                return 'success';
            case 'failed':
                return 'danger';
            case 'cancelled':
                return 'secondary';
            default:
                return 'primary';
        }
    };

    if (loading) {
        return <div>{t('loading_batches')}</div>;
    }

    return (
        <Container>
            <div className="d-flex justify-content-between align-items-center mb-4">
                <h2 className="mb-0">{t('coin_batches')}</h2>
                {getLastUpdateText(t) && (
                    <small className="text-muted">
                        {getLastUpdateText(t)}
                    </small>
                )}
            </div>

            {/* Search and Filter Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('data_actions')}</Form.Label>
                                        <div className="d-flex gap-2">
                                            <Button
                                                variant="primary"
                                                onClick={handleRefresh}
                                                disabled={refreshing}
                                                size="sm"
                                            >
                                                <FaSync className={`me-1 ${refreshing ? 'fa-spin' : ''}`} />
                                                {refreshing ? t('refreshing') : t('refresh')}
                                            </Button>
                                            <Button
                                                variant="success"
                                                onClick={exportToCSV}
                                                disabled={filteredBatches.length === 0}
                                                size="sm"
                                            >
                                                <FaDownload className="me-1" />
                                                {t('export')}
                                            </Button>
                                        </div>
                                    </Form.Group>
                                </Col>
                                <Col md={4}>
                                    <Form.Group>
                                        <Form.Label>{t('search_batch')}</Form.Label>
                                        <InputGroup>
                                            <Form.Control
                                                type="text"
                                                placeholder={t('search_agent_email_or_product_name')}
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                            />
                                        </InputGroup>
                                    </Form.Group>
                                </Col>
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('start_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('end_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Batches Table */}
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('batch_id')}</th>
                                        <th>{t('agent')}</th>
                                        <th>{t('product_name')}</th>
                                        <th>{t('currency')}</th>
                                        <th>{t('shares')}</th>
                                        <th>{t('batch_amount')}</th>
                                        <th>{t('per_share_amount')}</th>
                                        <th>{t('status')}</th>
                                        <th>{t('created_at')}</th>
                                        <th>{t('distributed_at')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedBatches.length === 0 ? (
                                        <tr>
                                            <td colSpan="11" className="text-center">{t('no_batches_available')}</td>
                                        </tr>
                                    ) : (
                                        paginatedBatches.map((batch) => (
                                            <tr key={batch.id}>
                                                <td>{batch.id.substring(0, 8)}...</td>
                                                <td>{batch.agent?.users?.email || '-'}</td>
                                                <td>{batch.product?.name || '-'}</td>
                                                <td>{batch.currency_code || '-'}</td>
                                                <td>{batch.shares ? Number(batch.shares).toFixed(2) : '0'}</td>
                                                <td>{batch.batch_amount ? Number(batch.batch_amount).toFixed(6) : '0'} {batch.currency_code}</td>
                                                <td>{batch.per_share_amount ? Number(batch.per_share_amount).toFixed(6) : '0'} {batch.currency_code}</td>
                                                <td>
                                                    <Badge bg={getStatusColor(batch.status)}>
                                                        {t(batch.status) || 'unknown'}
                                                    </Badge>
                                                </td>
                                                <td>{new Date(batch.created_at).toLocaleString()}</td>
                                                <td>{batch.distributed_at ? new Date(batch.distributed_at).toLocaleString() : '-'}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="d-flex justify-content-center">
                                    <Pagination>
                                        <Pagination.First
                                            onClick={() => handlePageChange(1)}
                                            disabled={currentPage === 1}
                                        />
                                        <Pagination.Prev
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                        />

                                        {/* Show page numbers */}
                                        {[...Array(totalPages)].map((_, index) => {
                                            const pageNumber = index + 1;
                                            // Show first page, last page, current page, and pages around current page
                                            if (
                                                pageNumber === 1 ||
                                                pageNumber === totalPages ||
                                                (pageNumber >= currentPage - 2 && pageNumber <= currentPage + 2)
                                            ) {
                                                return (
                                                    <Pagination.Item
                                                        key={pageNumber}
                                                        active={pageNumber === currentPage}
                                                        onClick={() => handlePageChange(pageNumber)}
                                                    >
                                                        {pageNumber}
                                                    </Pagination.Item>
                                                );
                                            } else if (
                                                pageNumber === currentPage - 3 ||
                                                pageNumber === currentPage + 3
                                            ) {
                                                return <Pagination.Ellipsis key={pageNumber} />;
                                            }
                                            return null;
                                        })}

                                        <Pagination.Next
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                        />
                                        <Pagination.Last
                                            onClick={() => handlePageChange(totalPages)}
                                            disabled={currentPage === totalPages}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default CoinBatches;
