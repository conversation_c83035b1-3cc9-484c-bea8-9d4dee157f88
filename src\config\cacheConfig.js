/**
 * 数据缓存配置
 * 定义不同类型数据的缓存策略
 */

export const CACHE_DURATIONS = {
    // 基础数据 - 缓存时间较长
    PRODUCTS: 10 * 60 * 1000,        // 产品列表 - 10分钟
    CURRENCIES: 30 * 60 * 1000,      // 货币列表 - 30分钟
    FACILITIES: 15 * 60 * 1000,      // 设施列表 - 15分钟
    
    // 用户相关数据 - 中等缓存时间
    USER_PROFILE: 5 * 60 * 1000,     // 用户资料 - 5分钟
    KYC_STATUS: 2 * 60 * 1000,       // KYC状态 - 2分钟
    
    // 交易和订单数据 - 较短缓存时间
    ORDERS: 3 * 60 * 1000,           // 订单列表 - 3分钟
    TRANSACTIONS: 2 * 60 * 1000,     // 交易记录 - 2分钟
    BATCHES: 5 * 60 * 1000,          // 批次数据 - 5分钟
    
    // 实时性要求高的数据 - 很短缓存时间
    BALANCES: 1 * 60 * 1000,         // 余额信息 - 1分钟
    MINER_SNAPSHOTS: 5 * 60 * 1000,  // 矿工快照 - 5分钟
    EARNINGS: 3 * 60 * 1000,         // 收益数据 - 3分钟
    
    // 统计数据 - 中等缓存时间
    NETWORK_STATS: 10 * 60 * 1000,   // 网络统计 - 10分钟
    REFERRAL_TREE: 5 * 60 * 1000,    // 推荐树 - 5分钟
    
    // 默认缓存时间
    DEFAULT: 5 * 60 * 1000           // 默认 - 5分钟
};

/**
 * 获取指定数据类型的缓存时间
 * @param {string} dataType - 数据类型
 * @returns {number} 缓存时间（毫秒）
 */
export const getCacheDuration = (dataType) => {
    return CACHE_DURATIONS[dataType] || CACHE_DURATIONS.DEFAULT;
};

/**
 * 缓存策略配置
 */
export const CACHE_STRATEGIES = {
    // 激进缓存 - 优先使用缓存，减少API调用
    AGGRESSIVE: {
        useStaleWhileRevalidate: true,  // 使用过期数据同时后台更新
        maxStaleTime: 2 * 60 * 1000,    // 最大过期时间2分钟
        backgroundRefresh: true          // 后台自动刷新
    },
    
    // 平衡缓存 - 平衡性能和数据新鲜度
    BALANCED: {
        useStaleWhileRevalidate: false,
        maxStaleTime: 1 * 60 * 1000,    // 最大过期时间1分钟
        backgroundRefresh: false
    },
    
    // 保守缓存 - 优先数据新鲜度
    CONSERVATIVE: {
        useStaleWhileRevalidate: false,
        maxStaleTime: 30 * 1000,        // 最大过期时间30秒
        backgroundRefresh: false
    }
};

/**
 * 根据数据类型获取推荐的缓存策略
 * @param {string} dataType - 数据类型
 * @returns {Object} 缓存策略配置
 */
export const getCacheStrategy = (dataType) => {
    // 实时性要求高的数据使用保守策略
    if (['BALANCES', 'EARNINGS'].includes(dataType)) {
        return CACHE_STRATEGIES.CONSERVATIVE;
    }
    
    // 基础数据使用激进策略
    if (['PRODUCTS', 'CURRENCIES', 'FACILITIES'].includes(dataType)) {
        return CACHE_STRATEGIES.AGGRESSIVE;
    }
    
    // 其他数据使用平衡策略
    return CACHE_STRATEGIES.BALANCED;
};

/**
 * 页面刷新触发条件
 */
export const REFRESH_TRIGGERS = {
    // 表单提交后需要刷新的数据类型
    FORM_SUBMIT: [
        'ORDERS',
        'TRANSACTIONS', 
        'BATCHES',
        'USER_PROFILE',
        'BALANCES'
    ],
    
    // 页面可见性变化时需要刷新的数据类型
    VISIBILITY_CHANGE: [
        'BALANCES',
        'EARNINGS',
        'ORDERS'
    ],
    
    // 用户操作后需要刷新的数据类型
    USER_ACTION: [
        'ORDERS',
        'TRANSACTIONS',
        'BALANCES',
        'BATCHES'
    ]
};

export default {
    CACHE_DURATIONS,
    CACHE_STRATEGIES,
    REFRESH_TRIGGERS,
    getCacheDuration,
    getCacheStrategy
};
