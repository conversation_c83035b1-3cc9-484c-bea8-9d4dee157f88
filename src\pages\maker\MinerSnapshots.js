import React, { useState, useEffect, useCallback } from 'react';
import { Container, Row, Col, Card, Table, Badge, Form, InputGroup, Pagination, Button } from 'react-bootstrap';
import { FaSync } from 'react-icons/fa';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import { useDataCache } from '../../hooks/useDataCache';

const MinerSnapshots = () => {
    const { t } = useTranslation();
    const [searchTerm, setSearchTerm] = useState('');
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [filteredSnapshots, setFilteredSnapshots] = useState([]);

    // Pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [snapshotsPerPage] = useState(10);
    const [paginatedSnapshots, setPaginatedSnapshots] = useState([]);

    // 数据获取函数
    const fetchSnapshots = useCallback(async () => {
        const supabase = getSupabase();
        if (!supabase) return null;

        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
            return null; // User not logged in
        }

        // Fetch miner daily snapshots with related information
        const { data, error } = await supabase
            .from('miner_daily_snapshots')
            .select(`
                miner_id,
                snapshot_date,
                blockchain_height,
                power,
                available_balance,
                pledge_locked,
                balance,
                miners (
                    filecoin_miner_id,
                    category,
                    facilities (
                        name
                    )
                )
            `)
            .order('snapshot_date', { ascending: false });

        if (error) {
            console.error('Error fetching miner snapshots:', error);
            throw error;
        }

        return data;
    }, []);

    // 使用数据缓存Hook
    const {
        data: snapshots,
        loading,
        refreshing,
        refresh,
        getLastUpdateText
    } = useDataCache(fetchSnapshots);

    // 初始化数据获取
    useEffect(() => {
        fetchSnapshots();
    }, [fetchSnapshots]);

    // Filter snapshots based on search criteria
    useEffect(() => {
        let filtered = snapshots || [];

        // Search by miner ID or facility name
        if (searchTerm) {
            filtered = filtered.filter(snapshot =>
                snapshot.miners?.filecoin_miner_id?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                snapshot.miners?.facilities?.name?.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Filter by date range
        if (startDate) {
            filtered = filtered.filter(snapshot =>
                new Date(snapshot.snapshot_date) >= new Date(startDate)
            );
        }
        if (endDate) {
            filtered = filtered.filter(snapshot =>
                new Date(snapshot.snapshot_date) <= new Date(endDate)
            );
        }

        setFilteredSnapshots(filtered);
        setCurrentPage(1); // Reset to first page when filters change
    }, [snapshots, searchTerm, startDate, endDate]);

    // Paginate filtered snapshots
    useEffect(() => {
        const indexOfLastSnapshot = currentPage * snapshotsPerPage;
        const indexOfFirstSnapshot = indexOfLastSnapshot - snapshotsPerPage;
        const currentSnapshots = filteredSnapshots.slice(indexOfFirstSnapshot, indexOfLastSnapshot);
        setPaginatedSnapshots(currentSnapshots);
    }, [filteredSnapshots, currentPage, snapshotsPerPage]);

    // Pagination handlers
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const totalPages = Math.ceil(filteredSnapshots.length / snapshotsPerPage);

    if (loading) {
        return <div>{t('loading_snapshots')}</div>;
    }

    return (
        <Container>
            <div className="d-flex justify-content-between align-items-center mb-4">
                <h2 className="mb-0">{t('miner_snapshots')}</h2>
                <div className="d-flex align-items-center gap-3">
                    {getLastUpdateText(t) && (
                        <small className="text-muted">
                            {getLastUpdateText(t)}
                        </small>
                    )}
                    <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={refresh}
                        disabled={refreshing}
                    >
                        <FaSync className={`me-1 ${refreshing ? 'fa-spin' : ''}`} />
                        {refreshing ? t('refreshing') : t('refresh')}
                    </Button>
                </div>
            </div>

            {/* Search and Filter Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={4}>
                                    <Form.Group>
                                        <Form.Label>{t('search_miner')}</Form.Label>
                                        <InputGroup>
                                            <Form.Control
                                                type="text"
                                                placeholder={t('search_miner_id_or_facility')}
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                            />
                                        </InputGroup>
                                    </Form.Group>
                                </Col>
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('start_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('end_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Snapshots Table */}
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('miner_id')}</th>
                                        <th>{t('facility')}</th>
                                        <th>{t('snapshot_date')}</th>
                                        <th>{t('blockchain_height')}</th>
                                        <th>{t('power')}</th>
                                        <th>{t('available_balance')}</th>
                                        <th>{t('pledge_locked')}</th>
                                        <th>{t('balance')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedSnapshots.length === 0 ? (
                                        <tr>
                                            <td colSpan="8" className="text-center">{t('no_snapshots_available')}</td>
                                        </tr>
                                    ) : (
                                        paginatedSnapshots.map((snapshot) => (
                                            <tr key={`${snapshot.miner_id}-${snapshot.snapshot_date}`}>
                                                <td>{snapshot.miners?.filecoin_miner_id || '-'}</td>
                                                <td>{snapshot.miners?.facilities?.name || '-'}</td>
                                                <td>{new Date(snapshot.snapshot_date).toLocaleDateString()}</td>
                                                <td>{snapshot.blockchain_height || '-'}</td>
                                                <td>{snapshot.power ? Number(snapshot.power).toFixed(2) : '0'} TiB</td>
                                                <td>{snapshot.available_balance ? Number(snapshot.available_balance).toFixed(6) : '0'} FIL</td>
                                                <td>{snapshot.pledge_locked ? Number(snapshot.pledge_locked).toFixed(6) : '0'} FIL</td>
                                                <td>{snapshot.balance ? Number(snapshot.balance).toFixed(6) : '0'} FIL</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="d-flex justify-content-center">
                                    <Pagination>
                                        <Pagination.First
                                            onClick={() => handlePageChange(1)}
                                            disabled={currentPage === 1}
                                        />
                                        <Pagination.Prev
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                        />

                                        {/* Show page numbers */}
                                        {[...Array(totalPages)].map((_, index) => {
                                            const pageNumber = index + 1;
                                            // Show first page, last page, current page, and pages around current page
                                            if (
                                                pageNumber === 1 ||
                                                pageNumber === totalPages ||
                                                (pageNumber >= currentPage - 2 && pageNumber <= currentPage + 2)
                                            ) {
                                                return (
                                                    <Pagination.Item
                                                        key={pageNumber}
                                                        active={pageNumber === currentPage}
                                                        onClick={() => handlePageChange(pageNumber)}
                                                    >
                                                        {pageNumber}
                                                    </Pagination.Item>
                                                );
                                            } else if (
                                                pageNumber === currentPage - 3 ||
                                                pageNumber === currentPage + 3
                                            ) {
                                                return <Pagination.Ellipsis key={pageNumber} />;
                                            }
                                            return null;
                                        })}

                                        <Pagination.Next
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                        />
                                        <Pagination.Last
                                            onClick={() => handlePageChange(totalPages)}
                                            disabled={currentPage === totalPages}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default MinerSnapshots;
