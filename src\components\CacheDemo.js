import React, { useState } from 'react';
import { <PERSON>, Button, Badge, Alert } from 'react-bootstrap';
import { FaSync, FaInfoCircle } from 'react-icons/fa';
import { useDataCache } from '../hooks/useDataCache';

/**
 * 缓存功能演示组件
 * 用于展示数据缓存的工作原理
 */
const CacheDemo = ({ title = "数据缓存演示", dataType = "DEFAULT" }) => {
    const [requestCount, setRequestCount] = useState(0);

    // 模拟数据获取函数
    const fetchDemoData = async () => {
        // 模拟API延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const newCount = requestCount + 1;
        setRequestCount(newCount);
        
        return {
            timestamp: new Date().toLocaleTimeString(),
            requestNumber: newCount,
            randomData: Math.floor(Math.random() * 1000)
        };
    };

    const {
        data,
        loading,
        refreshing,
        refresh,
        getLastUpdateText,
        cacheInfo
    } = useDataCache(fetchDemoData, dataType, {
        refreshOnVisibilityChange: true
    });

    return (
        <Card className="mb-3">
            <Card.Header className="d-flex justify-content-between align-items-center">
                <div>
                    <strong>{title}</strong>
                    <Badge variant="secondary" className="ms-2">
                        {dataType}
                    </Badge>
                </div>
                <div className="d-flex align-items-center gap-2">
                    {getLastUpdateText() && (
                        <small className="text-muted">
                            {getLastUpdateText()}
                        </small>
                    )}
                    <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={refresh}
                        disabled={refreshing}
                    >
                        <FaSync className={`me-1 ${refreshing ? 'fa-spin' : ''}`} />
                        {refreshing ? '刷新中...' : '刷新'}
                    </Button>
                </div>
            </Card.Header>
            <Card.Body>
                {loading ? (
                    <div className="text-center">
                        <div className="spinner-border spinner-border-sm me-2" />
                        加载中...
                    </div>
                ) : data ? (
                    <div>
                        <div className="row">
                            <div className="col-md-6">
                                <strong>数据内容:</strong>
                                <ul className="list-unstyled mt-2">
                                    <li>获取时间: {data.timestamp}</li>
                                    <li>请求次数: {data.requestNumber}</li>
                                    <li>随机数据: {data.randomData}</li>
                                </ul>
                            </div>
                            <div className="col-md-6">
                                <strong>缓存信息:</strong>
                                <ul className="list-unstyled mt-2">
                                    <li>缓存时长: {Math.floor(cacheInfo.cacheDuration / 1000)}秒</li>
                                    <li>数据状态: {cacheInfo.isStale ? '已过期' : '新鲜'}</li>
                                    <li>下次刷新: {cacheInfo.nextRefreshTime?.toLocaleTimeString() || '未知'}</li>
                                </ul>
                            </div>
                        </div>
                        
                        {cacheInfo.isStale && (
                            <Alert variant="warning" className="mt-3 mb-0">
                                <FaInfoCircle className="me-2" />
                                数据已过期，下次访问时将自动刷新
                            </Alert>
                        )}
                    </div>
                ) : (
                    <div className="text-muted">暂无数据</div>
                )}
            </Card.Body>
        </Card>
    );
};

export default CacheDemo;
