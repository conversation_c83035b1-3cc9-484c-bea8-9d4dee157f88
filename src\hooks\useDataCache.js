import { useState, useCallback, useRef, useEffect } from 'react';
import { getCacheDuration, getCacheStrategy } from '../config/cacheConfig';

/**
 * 通用数据缓存Hook
 * @param {Function} fetchFunction - 数据获取函数
 * @param {string|number} cacheConfig - 缓存配置：可以是数据类型字符串或缓存时间（毫秒）
 * @param {Object} options - 额外选项
 * @returns {Object} - 包含数据、加载状态、刷新函数等
 */
export const useDataCache = (fetchFunction, cacheConfig = 'DEFAULT', options = {}) => {
    // 解析缓存配置
    const cacheDuration = typeof cacheConfig === 'string'
        ? getCacheDuration(cacheConfig)
        : cacheConfig;

    const cacheStrategy = typeof cacheConfig === 'string'
        ? getCacheStrategy(cacheConfig)
        : { useStaleWhileRevalidate: false, maxStaleTime: 0, backgroundRefresh: false };
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [lastFetchTime, setLastFetchTime] = useState(null);
    const [error, setError] = useState(null);
    
    const hasMountedRef = useRef(false);

    // 数据获取函数，支持强制刷新和缓存策略
    const fetchData = useCallback(async (forceRefresh = false, silent = false) => {
        if (!fetchFunction) return;

        const now = Date.now();

        // 检查是否需要重新获取数据
        if (!forceRefresh && lastFetchTime) {
            const timeSinceLastFetch = now - lastFetchTime;

            // 如果在缓存时间内，直接返回
            if (timeSinceLastFetch < cacheDuration) {
                console.log('使用缓存数据，跳过API请求');
                return;
            }

            // 如果支持使用过期数据，在过期时间内继续使用旧数据
            if (cacheStrategy.useStaleWhileRevalidate &&
                timeSinceLastFetch < (cacheDuration + cacheStrategy.maxStaleTime)) {
                console.log('使用过期数据，后台更新中...');
                silent = true; // 后台静默更新
            }
        }

        const isInitialLoad = !hasMountedRef.current;
        if (!silent) {
            if (isInitialLoad) {
                setLoading(true);
            } else {
                setRefreshing(true);
            }
        }

        setError(null);

        try {
            const result = await fetchFunction();
            setData(result);
            setLastFetchTime(now);
            console.log('数据已更新，缓存时间:', new Date(now).toLocaleTimeString());
        } catch (err) {
            console.error('Error in fetchData:', err);
            setError(err);
        } finally {
            if (!silent) {
                if (isInitialLoad) {
                    setLoading(false);
                    hasMountedRef.current = true;
                } else {
                    setRefreshing(false);
                }
            }
        }
    }, [fetchFunction, lastFetchTime, cacheDuration, cacheStrategy]);

    // 手动刷新函数
    const refresh = useCallback(() => {
        fetchData(true);
    }, [fetchData]);

    // 清除缓存
    const clearCache = useCallback(() => {
        setLastFetchTime(null);
        setData(null);
        setError(null);
    }, []);

    // 格式化最后更新时间
    const getLastUpdateText = useCallback((t) => {
        if (!lastFetchTime) return '';
        const now = Date.now();
        const diffMinutes = Math.floor((now - lastFetchTime) / (1000 * 60));
        if (diffMinutes < 1) return t ? t('just_updated') : '刚刚更新';
        if (diffMinutes < 60) {
            return t ? t('updated_minutes_ago', { minutes: diffMinutes }) : `${diffMinutes}分钟前更新`;
        }
        const diffHours = Math.floor(diffMinutes / 60);
        return t ? t('updated_hours_ago', { hours: diffHours }) : `${diffHours}小时前更新`;
    }, [lastFetchTime]);

    // 检查数据是否过期
    const isDataStale = useCallback(() => {
        if (!lastFetchTime) return true;
        const now = Date.now();
        return (now - lastFetchTime) >= cacheDuration;
    }, [lastFetchTime, cacheDuration]);

    // 页面可见性变化时的处理
    useEffect(() => {
        if (!options.refreshOnVisibilityChange) return;

        const handleVisibilityChange = () => {
            if (!document.hidden && isDataStale()) {
                console.log('页面重新可见且数据过期，自动刷新');
                fetchData(false, true); // 静默刷新
            }
        };

        document.addEventListener('visibilitychange', handleVisibilityChange);
        return () => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
        };
    }, [fetchData, isDataStale, options.refreshOnVisibilityChange]);

    // 后台自动刷新
    useEffect(() => {
        if (!cacheStrategy.backgroundRefresh || !lastFetchTime) return;

        const interval = setInterval(() => {
            if (isDataStale()) {
                console.log('后台自动刷新数据');
                fetchData(false, true); // 静默刷新
            }
        }, cacheDuration);

        return () => clearInterval(interval);
    }, [fetchData, isDataStale, cacheStrategy.backgroundRefresh, cacheDuration, lastFetchTime]);

    return {
        data,
        loading,
        refreshing,
        error,
        lastFetchTime,
        fetchData,
        refresh,
        clearCache,
        getLastUpdateText,
        isDataStale,
        // 额外的状态信息
        cacheInfo: {
            cacheDuration,
            cacheStrategy,
            isStale: isDataStale(),
            nextRefreshTime: lastFetchTime ? new Date(lastFetchTime + cacheDuration) : null
        }
    };
};

export default useDataCache;
